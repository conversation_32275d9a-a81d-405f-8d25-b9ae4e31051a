<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_main.xml"
    app:startDestination="@id/splashFragment">

    <fragment
        android:id="@+id/splashFragment"
        android:name="co.cameralocation.framework.presentation.splash.SplashFragment"
        android:label="SplashFragment"
        tools:layout="@layout/fragment_splash">
        <action
            android:id="@+id/action_splashFragment_to_homeFragment"
            app:destination="@id/homeFragment" />
        <action
            android:id="@+id/action_splashFragment_to_languageFragment"
            app:destination="@id/languageFragment" />
    </fragment>

    <fragment
        android:id="@+id/homeFragment"
        android:name="co.cameralocation.framework.presentation.home.HomeFragment"
        android:label="HomeFragment"
        tools:layout="@layout/fragment_home">

        <action
            android:id="@+id/action_homeFragment_to_compassFragment"
            app:destination="@id/compassFragment" />
        <action
            android:id="@+id/action_homeFragment_to_liveVideoFragment"
            app:destination="@id/liveVideoFragment" />
        <action
            android:id="@+id/action_homeFragment_to_cameraGpsNewFragment"
            app:destination="@id/cameraGpsNewFragment" />
        <action
            android:id="@+id/action_homeFragment_to_generalSettingFragment"
            app:destination="@id/generalSettingFragment" />
    </fragment>

    <fragment
        android:id="@+id/languageFragment"
        android:name="co.cameralocation.framework.presentation.language.LanguageFragment"
        android:label="LanguageFragment"
        tools:layout="@layout/fragment_language">
        <action
            android:id="@+id/action_languageFragment_to_onboardingFragment"
            app:destination="@id/onboardingFragment"
            app:popUpTo="@id/splashFragment"
            app:popUpToInclusive="true" />
    </fragment>

    <fragment
        android:id="@+id/onboardingFragment"
        android:name="co.cameralocation.framework.presentation.onboarding.OnboardingFragment"
        android:label="OnboardingFragment"
        tools:layout="@layout/fragment_onboarding">
        <action
            android:id="@+id/action_onboardingFragment_to_homeFragment"
            app:destination="@id/homeFragment"
            app:popUpTo="@id/splashFragment"
            app:popUpToInclusive="true"  />
    </fragment>


    <fragment
        android:id="@+id/generalSettingFragment"
        android:name="co.cameralocation.framework.presentation.generalsetting.GeneralSettingFragment"
        android:label="GeneralSettingFragment"
        tools:layout="@layout/fragment_general_setting_screen" />

    <fragment
        android:id="@+id/compassFragment"
        android:name="co.cameralocation.framework.presentation.compass.CompassFragment"
        android:label="CompassFragment"
        tools:layout="@layout/fragment_compass" />

    <fragment
        android:id="@+id/liveVideoFragment"
        android:name="co.cameralocation.framework.presentation.livevideo.LiveVideoFragment"
        android:label="LiveVideoFragment"
        tools:layout="@layout/fragment_live_video" />

    <!--<fragment
        android:id="@+id/cameraGpsFragment"
        android:name="co.cameralocation.framework.presentation.cameragps.old.CameraGpsFragment"
        android:label="CameraGpsFragment"
        tools:layout="@layout/fragment_edit_location_new">
        <action
            android:id="@+id/action_cameraGpsFragment_to_gridPhotoFragment"
            app:destination="@id/gridPhotoFragment" />
        <action
            android:id="@+id/action_cameraGpsFragment_to_cameraFragment"
            app:destination="@id/cameraFragment" />
        <action
            android:id="@+id/action_cameraGpsFragment_to_savePhotoFragment"
            app:destination="@id/savePhotoFragment" />
        <action
            android:id="@+id/action_cameraGpsFragment_to_editLocationFragment"
            app:destination="@id/editLocationFragment" />
    </fragment>-->

    <!--<fragment
        android:id="@+id/editLocationFragment"
        android:name="co.cameralocation.framework.presentation.editlocation.old.EditLocationFragment"
        android:label="EditLocationFragment"
        tools:layout="@layout/fragment_edit_location">
        <action
            android:id="@+id/action_editLocationFragment_to_changeLocationFragment"
            app:destination="@id/changeLocationFragment" />
    </fragment>-->

    <fragment
        android:id="@+id/chooseLocationFragment"
        android:name="co.cameralocation.framework.presentation.chooselocation.ChooseLocationFragment"
        android:label="ChooseLocationFragment"
        tools:layout="@layout/fragment_choose_location" />

    <!--<fragment
        android:id="@+id/changeLocationFragment"
        android:name="co.cameralocation.framework.presentation.changelocation.ChangeLocationFragment"
        android:label="changeLocationFragment"
        tools:layout="@layout/fragment_change_location">
    </fragment>-->

    <fragment
        android:id="@+id/gridPhotoFragment"
        android:name="co.cameralocation.framework.presentation.gridphoto.GridPhotoFragment"
        android:label="GridPhotoFragment"
        tools:layout="@layout/fragment_grid_photo">
        <action
            android:id="@+id/action_gridPhotoFragment_to_pickPhotoFragment"
            app:destination="@id/pickPhotoFragment" />
    </fragment>

    <fragment
        android:id="@+id/pickPhotoFragment"
        android:name="co.cameralocation.framework.presentation.pickphoto.PickPhotoFragment"
        android:label="PickPhotoFragment"
        tools:layout="@layout/fragment_pick_photo">
        <action
            android:id="@+id/action_pickPhotoFragment_to_editGridPhotoFragment"
            app:destination="@id/editGridPhotoFragment" />
    </fragment>

    <fragment
        android:id="@+id/editGridPhotoFragment"
        android:name="co.cameralocation.framework.presentation.editgridphoto.EditGridPhotoFragment"
        android:label="EditGridPhotoFragment"
        tools:layout="@layout/fragment_edit_grid_photo">
        <!--<action
            android:id="@+id/action_editGridPhotoFragment_to_changeLocationFragment"
            app:destination="@id/changeLocationFragment" />-->
        <action
            android:id="@+id/action_editGridPhotoFragment_to_editLocationNewFragment"
            app:destination="@id/editLocationNewFragment" />
        <action
            android:id="@+id/action_editGridPhotoFragment_to_savePhotoFragment"
            app:destination="@id/savePhotoFragment"
            app:popUpTo="@id/cameraGpsNewFragment"
            app:popUpToInclusive="false" />
    </fragment>

    <fragment
        android:id="@+id/cameraFragment"
        android:name="co.cameralocation.framework.presentation.camera.CameraFragment"
        android:label="CameraFragment"
        tools:layout="@layout/fragment_camera" >
        <action
            android:id="@+id/action_cameraFragment_to_savePhotoFragment"
            app:destination="@id/savePhotoFragment" />
        <!--<action
            android:id="@+id/action_cameraFragment_to_changeLocationFragment"
            app:destination="@id/changeLocationFragment" />-->
        <action
            android:id="@+id/action_cameraFragment_to_editLocationNewFragment"
            app:destination="@id/editLocationNewFragment" />
    </fragment>

    <fragment
        android:id="@+id/editLocationNewFragment"
        android:name="co.cameralocation.framework.presentation.editlocation.EditLocationNewFragment"
        android:label="EditLocationNewFragment"
        tools:layout="@layout/fragment_edit_location_new" >
        <action
            android:id="@+id/action_editLocationNewFragment_to_chooseLocationFragment"
            app:destination="@id/chooseLocationFragment" />
    </fragment>

    <fragment
        android:id="@+id/savePhotoFragment"
        android:name="co.cameralocation.framework.presentation.savemedia.SaveMediaFragment"
        android:label="SaveMediaFragment"
        tools:layout="@layout/fragment_save_media">
        <action
            android:id="@+id/action_saveMediaFragment_to_editMediaPhotoFragment"
            app:destination="@id/editMediaPhotoFragment" />
        <action
            android:id="@+id/action_saveMediaFragment_to_editMediaVideoFragment"
            app:destination="@id/editMediaVideoFragment" />
    </fragment>

    <fragment
        android:id="@+id/editMediaPhotoFragment"
        android:name="co.cameralocation.framework.presentation.editmediaphoto.EditMediaPhotoFragment"
        android:label="EditMediaPhotoFragment"
        tools:layout="@layout/fragment_edit_media_photo" />

    <fragment
        android:id="@+id/editMediaVideoFragment"
        android:name="co.cameralocation.framework.presentation.editmediavideo.EditMediaVideoFragment"
        android:label="EditMediaVideoFragment"
        tools:layout="@layout/fragment_edit_media_video" />

    <fragment
        android:id="@+id/cameraGpsNewFragment"
        android:name="co.cameralocation.framework.presentation.cameragps.CameraGPSNewFragment"
        android:label="CameraGPSNewFragment"
        tools:layout="@layout/fragment_camera_gps_new">
        <action
            android:id="@+id/action_cameraGpsNewFragment_to_homeFragment"
            app:destination="@id/homeFragment" />
        <action
            android:id="@+id/action_cameraGpsNewFragment_to_cameraFragment"
            app:destination="@id/cameraFragment" />
        <action
            android:id="@+id/action_cameraGpsNewFragment_to_gridPhotoFragment"
            app:destination="@id/gridPhotoFragment" />
        <action
            android:id="@+id/action_cameraGpsNewFragment_to_savePhotoFragment"
            app:destination="@id/savePhotoFragment" />
    </fragment>
</navigation>