<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools">
    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:padding="@dimen/_8dp"
        android:orientation="horizontal"
        android:gravity="bottom">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/btnEdit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingHorizontal="@dimen/_10dp"
                android:paddingVertical="@dimen/_2dp"
                android:textSize="@dimen/_16sp"
                android:layout_marginEnd="@dimen/_17dp"
                android:fontFamily="@font/font_700"
                android:textColor="@color/blue_3e9df4"
                android:layout_gravity="end"
                android:background="@drawable/bg_button_edit_edit_location_screen"
                android:backgroundTint="#99FFD656"
                android:drawableStart="@drawable/ic_pencil_edit_location"
                android:drawablePadding="@dimen/_6dp"
                android:text="@string/edit"/>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/bg_radius_14"
                android:backgroundTint="#99FFD656"
                android:paddingVertical="@dimen/_6dp"
                android:layout_marginStart="@dimen/_6dp">

                <co.cameralocation.customview.RoundedImageView
                    android:layout_width="@dimen/_63dp"
                    android:layout_height="@dimen/_63dp"
                    android:src="@drawable/img_location_icon_default"
                    app:cornerRadius="@dimen/_6dp"
                    android:layout_gravity="center"
                    android:layout_marginStart="@dimen/_12dp" />

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical"
                    android:layout_marginEnd="@dimen/_10dp"
                    android:layout_gravity="center"
                    android:paddingStart="@dimen/_8dp">

                    <TextView
                        android:id="@+id/currentLocationTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        tools:text="Home"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_14sp" />

                    <TextView
                        android:id="@+id/currentLocationName"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/font_400"
                        android:text="@string/sample_location_name"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_14sp" />

                    <TextView
                        android:id="@+id/currentLocationCoordinates"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/sample_coordinates"
                        android:fontFamily="@font/font_400"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_12sp" />

                    <TextView
                        android:id="@+id/currentLocationTimestamp"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/sample_timestamp"
                        android:fontFamily="@font/font_400"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_12sp" />

                    <TextView
                        android:id="@+id/currentLocationWeather"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        tools:text="25°C, Sunny"
                        android:fontFamily="@font/font_400"
                        android:textColor="@android:color/white"
                        android:textSize="@dimen/_12sp" />
                </LinearLayout>


            </LinearLayout>
        </LinearLayout>


    </LinearLayout>
</layout>
