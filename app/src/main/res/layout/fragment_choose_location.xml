<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/black_121825">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/_6dp"
            android:layout_marginTop="@dimen/_6dp"
            android:padding="@dimen/_10dp"
            android:src="@drawable/ic_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <!-- Title -->
        <TextView
            android:id="@+id/titleText"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/_7dp"
            android:fontFamily="@font/font_700"
            android:text="@string/edit_location"
            android:textColor="@android:color/white"
            android:textSize="@dimen/_18sp"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"
            app:layout_constraintEnd_toStartOf="@+id/btnDone"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintStart_toEndOf="@id/btnBack"
            app:layout_constraintTop_toTopOf="@id/btnBack" />

        <!-- Done button -->
        <TextView
            android:id="@+id/btnDone"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/_16dp"
            android:fontFamily="@font/font_700"
            android:paddingVertical="@dimen/_8dp"
            android:paddingHorizontal="@dimen/_12dp"
            android:background="@drawable/bg_radius_8"
            android:backgroundTint="@color/blue_3e9df4"
            android:text="@string/done"
            android:textColor="@color/white"
            android:textSize="@dimen/_16sp"
            android:textAllCaps="true"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/btnBack"
            app:layout_constraintBottom_toBottomOf="@id/btnBack"/>

        <!-- Map for Choose Location Tab -->
        <fragment
            android:id="@+id/mapChooseLocation"
            android:name="com.google.android.gms.maps.SupportMapFragment"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/btnBack"
            android:layout_marginTop="@dimen/_16dp"/>

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toTopOf="@id/linearLayout"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/mapChooseLocation"
            android:orientation="vertical">

            <androidx.cardview.widget.CardView
                android:id="@+id/search_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/_10dp"
                app:cardCornerRadius="@dimen/_8dp"
                app:cardElevation="@dimen/_4dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/mapChooseLocation">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <EditText
                        android:id="@+id/search_input"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/_48dp"
                        android:layout_weight="1"
                        android:background="@null"
                        android:hint="@string/search_places"
                        android:imeOptions="actionSearch"
                        android:inputType="text"
                        android:paddingStart="@dimen/_16dp"
                        android:paddingEnd="@dimen/_16dp"
                        android:singleLine="true" />

                    <ImageButton
                        android:id="@+id/search_button"
                        android:layout_width="@dimen/_48dp"
                        android:layout_height="@dimen/_48dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                        android:src="@android:drawable/ic_menu_search" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/places_recycler_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/_8dp"
                android:layout_marginHorizontal="@dimen/_10dp"
                android:background="@android:color/white"
                android:visibility="visible"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager" />

        </LinearLayout>

        <com.google.android.material.floatingactionbutton.FloatingActionButton
            android:id="@+id/toggleMapTypeButton"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toTopOf="@id/linearLayout"
            app:layout_constraintEnd_toEndOf="parent"
            android:layout_margin="16dp"
            android:backgroundTint="@color/blue_28abf5"
            android:src="@drawable/ic_layers"
            android:tint="@android:color/white" />

        <LinearLayout
            android:id="@+id/linearLayout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/bg_bottom_choose_location_screen"
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="parent">

            <ImageView
                android:layout_width="@dimen/_40dp"
                android:layout_height="@dimen/_40dp"
                android:layout_marginVertical="@dimen/_20dp"
                android:layout_marginStart="@dimen/_16dp"
                android:src="@drawable/ic_location_save_media_screen"
                app:tint="@color/blue_28abf5" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/_8dp"
                android:layout_marginTop="@dimen/_20dp"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCurrentLocationName"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:ellipsize="marquee"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:fontFamily="@font/font_700"
                    android:marqueeRepeatLimit="marquee_forever"
                    android:scrollHorizontally="true"
                    android:singleLine="true"
                    android:textColor="@android:color/white"
                    android:textSize="@dimen/_16sp"
                    tools:text="New York, USA" />

                <TextView
                    android:id="@+id/tvCurrentLocationCoordinates"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/font_400"
                    android:textColor="#8C8C8B"
                    android:textSize="@dimen/_14sp"
                    tools:text="40.7128° N, 74.0060° W" />
            </LinearLayout>
        </LinearLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
