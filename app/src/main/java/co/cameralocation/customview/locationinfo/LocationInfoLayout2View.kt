package co.cameralocation.customview.locationinfo

import android.content.Context
import android.graphics.Bitmap
import android.util.AttributeSet
import androidx.core.view.isVisible
import co.cameralocation.databinding.LayoutItemRecentLocationCameraGpsLayout2EditableBinding
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.util.marquee
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.util.stopMarquee

class LocationInfoLayout2View @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : BaseLocationInfoEditableLayoutView<LayoutItemRecentLocationCameraGpsLayout2EditableBinding>(
    LayoutItemRecentLocationCameraGpsLayout2EditableBinding::inflate,
    context,
    attrs,
    defStyleAttr
) {
    override fun setLocationTitle(title: String?) {
        if (title.isNullOrEmpty()) {
            binding.currentLocationTitle.isVisible = false
        } else {
            binding.currentLocationTitle.isVisible = true
            binding.currentLocationTitle.text = title
        }
        requestLayout()
    }

    override fun setLocationDisplayName(displayName: String) {
        binding.currentLocationName.text = displayName
        invalidate()
    }

    override fun setLocationCoordinates(coordinates: String) {
        binding.currentLocationCoordinates.text = coordinates
        binding.currentLocationCoordinates.marquee()
        invalidate()
    }

    override fun setLocationTimestamp(timestamp: String) {
        binding.currentLocationTimestamp.text = timestamp
        binding.currentLocationTimestamp.marquee()
        invalidate()
    }

    override fun setWeatherInfo(temperature: Double?, condition: String?) {
        val weatherText = if (temperature != null && condition != null) {
            "${temperature.toInt()}°C, $condition"
        } else {
            ""
        }
        binding.currentLocationWeather.text = weatherText
        binding.currentLocationWeather.marquee()
        invalidate()
    }

    override fun setVisibilityTitle(isVisible: Boolean) {
        binding.currentLocationName.visibility = if (isVisible) VISIBLE else INVISIBLE
        invalidate()
    }

    override fun setVisibilityCoordinates(isVisible: Boolean) {
        binding.currentLocationCoordinates.visibility = if (isVisible) VISIBLE else INVISIBLE
        invalidate()
    }

    override fun setVisibilityDateTime(isVisible: Boolean) {
        binding.currentLocationTimestamp.visibility = if (isVisible) VISIBLE else INVISIBLE
        invalidate()
    }

    override fun setVisibilityWeather(isVisible: Boolean) {
        binding.currentLocationWeather.visibility = if (isVisible) VISIBLE else INVISIBLE
        invalidate()
    }

    override fun setVisibilityEditButton(isVisible: Boolean) {
        binding.btnEdit.visibility = if (isVisible) VISIBLE else INVISIBLE
        invalidate()
    }

    override fun setOnClickEditButtonListener(listener: () -> Unit) {
        binding.btnEdit.setPreventDoubleClick {
            listener.invoke()
        }
    }
    
    /**
     * Tạo một bản sao của view hiện tại với cùng thuộc tính
     * @return Một instance mới của LocationInfoLayout2View với cùng thuộc tính
     */
    override fun clone(): BaseLocationInfoEditableLayoutView<LayoutItemRecentLocationCameraGpsLayout2EditableBinding> {
        // Tạo một instance mới của LocationInfoLayout2View
        val clonedView = LocationInfoLayout2View(context)
        
        // Sao chép các thuộc tính cơ bản từ lớp cha
        copyPropertiesTo(clonedView)
        
        // Sao chép các thuộc tính cụ thể của LocationInfoLayout2View
        if (binding.currentLocationTitle.isVisible) {
            clonedView.setLocationTitle(binding.currentLocationTitle.text.toString())
        } else {
            clonedView.setLocationTitle(null)
        }
        
        clonedView.setLocationDisplayName(binding.currentLocationName.text.toString())
        clonedView.setLocationCoordinates(binding.currentLocationCoordinates.text.toString())
        clonedView.setLocationTimestamp(binding.currentLocationTimestamp.text.toString())
        
        // Xử lý thông tin thời tiết
        val weatherText = binding.currentLocationWeather.text.toString()
        if (weatherText.isNotEmpty()) {
            // Trích xuất nhiệt độ và điều kiện từ chuỗi thời tiết
            val regex = "(\\d+)°C, (.+)".toRegex()
            val matchResult = regex.find(weatherText)
            if (matchResult != null) {
                val (temperature, condition) = matchResult.destructured
                clonedView.setWeatherInfo(temperature.toDouble(), condition)
            }
        } else {
            clonedView.setWeatherInfo(null, null)
        }
        
        // Sao chép trạng thái hiển thị
        clonedView.setVisibilityTitle(binding.currentLocationName.isVisible)
        clonedView.setVisibilityCoordinates(binding.currentLocationCoordinates.isVisible)
        clonedView.setVisibilityDateTime(binding.currentLocationTimestamp.isVisible)
        clonedView.setVisibilityWeather(binding.currentLocationWeather.isVisible)
        clonedView.setVisibilityEditButton(binding.btnEdit.isVisible)
        
        return clonedView
    }
}