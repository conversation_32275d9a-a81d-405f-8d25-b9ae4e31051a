package co.cameralocation.customview.locationinfo

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.core.graphics.createBitmap
import androidx.databinding.ViewDataBinding
import co.cameralocation.framework.presentation.common.Inflate

abstract class BaseLocationInfoEditableLayoutView<T : ViewDataBinding> @JvmOverloads constructor(
    protected val bindingInflater: Inflate<T>,
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var _binding: T? = null
    val binding: T
        get() = checkNotNull(_binding) {
            "BaseLocationInfoEditableLayout binding cannot be accessed before onCreateView() or after onDestroyView()"
        }

    init {
        initBinding()
    }

    private fun initBinding() {
        _binding = bindingInflater(LayoutInflater.from(context), this, false)

        val layoutParams = LayoutParams(
            LayoutParams.MATCH_PARENT,
            LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
            bottomMargin = 32 // px, hoặc dùng context.dp(32)
        }

        addView(binding.root, layoutParams)
    }

    fun createBitmapFromView(): Bitmap {
        val bitmap = createBitmap(binding.root.width, binding.root.height).apply {
            val canvas = Canvas(this)
            binding.root.draw(canvas)
        }

        return bitmap
    }

    abstract fun setLocationTitle(title: String?)
    abstract fun setLocationDisplayName(displayName: String)
    abstract fun setLocationCoordinates(coordinates: String)
    abstract fun setLocationTimestamp(timestamp: String)
    abstract fun setWeatherInfo(temperature: Double?, condition: String?)
    abstract fun setVisibilityTitle(isVisible: Boolean)
    abstract fun setVisibilityCoordinates(isVisible: Boolean)
    abstract fun setVisibilityDateTime(isVisible: Boolean)
    abstract fun setVisibilityWeather(isVisible: Boolean)
    abstract fun setVisibilityEditButton(isVisible: Boolean)
    abstract fun setOnClickEditButtonListener(listener: () -> Unit)
    
    /**
     * Tạo một bản sao của view hiện tại với cùng thuộc tính
     * @return Một instance mới của view với cùng loại và thuộc tính
     */
    abstract fun clone(): BaseLocationInfoEditableLayoutView<T>
    
    /**
     * Sao chép các thuộc tính từ view hiện tại sang view đích
     * @param target View đích để sao chép thuộc tính vào
     */
    fun copyPropertiesTo(target: BaseLocationInfoEditableLayoutView<T>) {
        // Sao chép các thuộc tính cơ bản
        target.visibility = this.visibility
        target.alpha = this.alpha
        target.translationX = this.translationX
        target.translationY = this.translationY
        target.scaleX = this.scaleX
        target.scaleY = this.scaleY
        target.rotation = this.rotation
        target.rotationX = this.rotationX
        target.rotationY = this.rotationY
        target.pivotX = this.pivotX
        target.pivotY = this.pivotY
        
        // Các lớp con sẽ cần triển khai phương thức clone() để sao chép các thuộc tính cụ thể
    }
}