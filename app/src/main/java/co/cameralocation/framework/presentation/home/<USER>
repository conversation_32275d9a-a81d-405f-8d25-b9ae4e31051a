package co.cameralocation.framework.presentation.home

import co.cameralocation.R
import co.cameralocation.framework.presentation.dialog.FeatureComingSoonDialogFragment
import co.cameralocation.util.isAllPermissionGranted
import co.cameralocation.util.isGpsEnabled
import co.cameralocation.util.marquee
import co.cameralocation.util.requestEnableGps
import co.cameralocation.util.requestPermission
import co.cameralocation.util.setPreventDoubleClick


fun HomeFragment.setCompassEvent() {
    binding.itemCompass.layoutItem.setPreventDoubleClick {
        /*if (isAllPermissionGranted(permissions)) {
            checkGpsAndNextAction {
                safeNav(R.id.homeFragment, R.id.action_homeFragment_to_compassFragment)
            }
        } else {
            requestPermission(permissions) {
                if (isAllPermissionGranted(permissions)) {
                    checkGpsAndNextAction {
                        //Do nothing
                    }
                }
            }
        }*/
        FeatureComingSoonDialogFragment().show(childFragmentManager, FeatureComingSoonDialogFragment.TAG)
    }
}

fun HomeFragment.setLiveVideoEvent() {
    binding.itemLiveVideo.layoutItem.setPreventDoubleClick {
        /*if (isAllPermissionGranted(permissions)) {
            checkGpsAndNextAction {
                safeNav(R.id.homeFragment, R.id.action_homeFragment_to_liveVideoFragment)
            }
        } else {
            requestPermission(permissions) {
                if (isAllPermissionGranted(permissions)) {
                    checkGpsAndNextAction {
                        //Do nothing
                    }
                }
            }
        }*/
        FeatureComingSoonDialogFragment().show(childFragmentManager, FeatureComingSoonDialogFragment.TAG)
    }
}

fun HomeFragment.setCameraGPSEvent() {
    binding.itemCameraGPS.layoutItem.setPreventDoubleClick {
        if (isAllPermissionGranted(permissions)) {
            checkGpsAndNextAction {
                safeNav(R.id.homeFragment, R.id.action_homeFragment_to_cameraGpsNewFragment)
            }
        } else {
            requestPermission(permissions) {
                checkGpsAndNextAction {
                    //Do nothing
                }
            }
        }
    }
}

fun HomeFragment.setIapEvent() {
    binding.iconIap.setPreventDoubleClick {
        //Todo: Implement IAP
    }
}

fun HomeFragment.setSettingsEvent() {
    binding.iconSettings.setPreventDoubleClick {
        safeNav(R.id.homeFragment, R.id.action_homeFragment_to_generalSettingFragment)
    }
}

/**
 * Helper function to check if GPS is enabled and request it if not
 * @param onNextAction The action to perform after GPS is enabled
 */
fun HomeFragment.checkGpsAndNextAction(onNextAction: () -> Unit) {
    if (requireContext().isGpsEnabled()) {
        // GPS is already enabled, proceed with navigation
        onNextAction.invoke()
    } else {
        requestEnableGps {
            //Do nothing
        }
    }
}

fun HomeFragment.initButtonData() {
    binding.itemCameraGPS.imageBackground.setImageResource(R.drawable.img_background_home_camera_gps)
    binding.itemCameraGPS.imageIcon.setImageResource(R.drawable.ic_home_camera_gps_icon)
    binding.itemCameraGPS.textViewTitle.text = getString(R.string.home_camera_gps)
    binding.itemCameraGPS.textViewTitle.marquee()
    binding.itemCameraGPS.textViewSubtitles.text = getString(R.string.home_camera_gps_sub)

    binding.itemLiveVideo.imageBackground.setImageResource(R.drawable.img_background_home_live_video)
    binding.itemLiveVideo.imageIcon.setImageResource(R.drawable.ic_home_video_live_icon)
    binding.itemLiveVideo.textViewTitle.text = getString(R.string.home_live_video)
    binding.itemLiveVideo.textViewTitle.marquee()
    binding.itemLiveVideo.textViewSubtitles.text = getString(R.string.home_live_video_sub)

    binding.itemCompass.imageBackground.setImageResource(R.drawable.img_background_home_compass)
    binding.itemCompass.imageIcon.setImageResource(R.drawable.ic_home_compass_icon)
    binding.itemCompass.textViewTitle.text = getString(R.string.home_compass)
    binding.itemCompass.textViewTitle.marquee()
    binding.itemCompass.textViewSubtitles.text = getString(R.string.home_compass_sub)
}