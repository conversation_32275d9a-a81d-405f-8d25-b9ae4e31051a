package co.cameralocation.framework.presentation.editlayout

import androidx.recyclerview.widget.LinearLayoutManager
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.map
import co.cameralocation.R
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_COORDINATES
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_DATETIME
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_LOCATION
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_TITLE
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment.Companion.ITEM_ID_WEATHER
import co.cameralocation.framework.presentation.editlayout.adapter.LayoutSettingAdapter
import co.cameralocation.framework.presentation.editlayout.model.LayoutSettingItem
import co.cameralocation.util.setPreventDoubleClick
import timber.log.Timber

fun EditLayoutDialogFragment.observeTitle() {
    launchMain {
        viewModel.uiState
            .map { it.defaultUnknownLocationTitle }
            .distinctUntilChanged()
            .collectLatest { titleUiState ->
                layoutSettingAdapter.updateTitleText(titleUiState)
                onUpdateDefaultTitle(titleUiState.title)
            }
    }
}

fun EditLayoutDialogFragment.observeShowCoordinates() {
    launchMain {
        viewModel.uiState
            .map { it.showCoordinates }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_COORDINATES, show)
                //commonViewModel.updateShowCoordinatesVisibilityForLayoutGlobally(show)
                onUpdateShowCoordinates(show)
            }
    }
}

fun EditLayoutDialogFragment.observeShowDateTime() {
    launchMain {
        viewModel.uiState
            .map { it.showDateTime }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_DATETIME, show)
                //commonViewModel.updateShowDateTimeVisibilityForLayoutGlobally(show)
                onUpdateShowDateTime(show)
            }
    }
}

fun EditLayoutDialogFragment.observeShowWeather() {
    launchMain {
        viewModel.uiState
            .map { it.showWeather }
            .filterNotNull()
            .distinctUntilChanged()
            .collectLatest { show ->
                layoutSettingAdapter.updateSwitchState(ITEM_ID_WEATHER, show)
                //commonViewModel.updateShowWeatherVisibilityForLayoutGlobally(show)
                onUpdateShowWeather(show)
            }
    }
}

fun EditLayoutDialogFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        onCloseDialog(hasUpdateData)
        dismiss()
    }
}

fun EditLayoutDialogFragment.setupRecyclerView() {
    layoutSettingAdapter = LayoutSettingAdapter().apply {
        onSwitchChanged = { item, isChecked ->
            hasUpdateData = true
            Timber.d("LayoutSettingAdapter.onSwitchChanged: ${item.title}, isChecked=$isChecked")
            when (item.id) {
                ITEM_ID_COORDINATES -> viewModel.toggleShowCoordinates(isChecked)
                ITEM_ID_DATETIME -> viewModel.toggleShowDateTime(isChecked)
                ITEM_ID_WEATHER -> viewModel.toggleShowWeather(isChecked)

            }
        }
        onSaveTitle = { title ->
            hasUpdateData = true
            Timber.d("onSaveTitle: $title")
            viewModel.saveDefaultUnknownLocationTitleGlobally(title)
        }
    }

    binding.settingsRecyclerView.apply {
        layoutManager = LinearLayoutManager(requireContext())
        adapter = layoutSettingAdapter
    }

    // Create initial settings list
    val settings = listOf(
        LayoutSettingItem(
            id = ITEM_ID_TITLE,
            title = getString(R.string.show_title),
            description = getString(R.string.edit_title),
            isEnabled = itemData?.isTitleVisible == true,
            isFixed = true,
            hasEditText = true
        ),
        LayoutSettingItem(
            id = ITEM_ID_LOCATION,
            title = getString(R.string.show_location),
            description = "",
            isEnabled = itemData?.isLocationVisible == true,
            isFixed = true,
            hasEditText = false
        ),
        LayoutSettingItem(
            id = ITEM_ID_COORDINATES,
            title = getString(R.string.show_coordinates),
            description = "",
            isEnabled = itemData?.isCoordinatesVisible == true,
            isFixed = false,
            hasEditText = false
        ),
        LayoutSettingItem(
            id = ITEM_ID_DATETIME,
            title = getString(R.string.show_datetime),
            description = "",
            isEnabled = itemData?.isDateTimeVisible == true,
            isFixed = false,
            hasEditText = false
        ),
        LayoutSettingItem(
            id = ITEM_ID_WEATHER,
            title = getString(R.string.show_weather),
            description = "",
            isEnabled = itemData?.isWeatherVisible == true,
            isFixed = false,
            hasEditText = false
        )
    )

    layoutSettingAdapter.submitList(settings)
}
