package co.cameralocation.framework.presentation.camera

import android.Manifest
import android.view.View
import androidx.core.view.isVisible
import com.otaliastudios.cameraview.CameraView
import dagger.hilt.android.AndroidEntryPoint
import co.cameralocation.customview.locationinfo.BaseLocationInfoEditableLayoutView
import co.cameralocation.databinding.FragmentCameraBinding
import co.cameralocation.framework.manager.LocationManager
import co.cameralocation.framework.presentation.common.BaseFragment
import java.util.concurrent.atomic.AtomicInteger
import javax.inject.Inject

@AndroidEntryPoint
class CameraFragment : BaseFragment<FragmentCameraBinding, CameraViewModel>(
    FragmentCameraBinding::inflate,
    CameraViewModel::class.java
) {
    @Inject
    lateinit var locationManager: LocationManager

    var isRequestSaveFullVideoFileAfterSavingPartDone = false
    var partIndex = AtomicInteger(0)
    var cameraView: CameraView? = null

    var currentLocationView: BaseLocationInfoEditableLayoutView<*>? = null

    val permissions = listOf(
        Manifest.permission.CAMERA,
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION
    )

    override fun init(view: View) {

        //TODO: BA request hide settings ver 1.0.0
        binding.btnSettings.isVisible = false
        setUpCurrentLocation()
        loadCameraSettingsPreferences()
        setupBackButton()
        setUpCameraView()
        setupGridButton()
        setupCaptureButton()
        setupSwitchCameraButton()
        setupFlashButton()
        setupRatioSpinner()
        setupSwitchModeButton()
        setupSettingsButton()
        setupTimerButton()
        setupPauseResumeButton()
        setupButtonSaveMediaEvent()
    }

    override fun subscribeObserver(view: View) {
        observeCameraMode()
        observeCameraFacing()
        observeFlashMode()
        observeGridMode()
        observeAspectRatio()
        observeRecordingState()
        observeTimerState()
        observeTimeButtonVisibilityChanged()
        observeRecordingTimerVisibilityChanged()
        observeSavingStateChanged()
        observePictureCountdownTimerTextVisibilityChanged()
        observeCurrentLocationItem()
        observePauseResumeButtonVisibilityChanged()
        observeSwitchFacingButtonVisibilityChanged()
        observeGoToSaveMediaButtonVisibilityChanged()
        observeToggleBetweenPictureAndVideoModeChanged()
        observeEditButtonVisibilityChanged()
    }

    companion object {
        const val TAG = "CameraFragment"
    }
}

