package co.cameralocation.framework.presentation.chooselocation

import android.Manifest
import android.os.Parcelable
import android.view.View
import com.google.android.gms.location.FusedLocationProviderClient
import com.google.android.gms.maps.GoogleMap
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.parcelize.Parcelize
import co.cameralocation.databinding.FragmentChooseLocationBinding
import co.cameralocation.framework.manager.LocationManager
import co.cameralocation.framework.presentation.chooselocation.adapter.PlacesAdapter
import co.cameralocation.framework.presentation.common.BaseFragment
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.util.Constant
import co.cameralocation.util.parcelable
import timber.log.Timber
import javax.inject.Inject

@AndroidEntryPoint
class ChooseLocationFragment : BaseFragment<FragmentChooseLocationBinding, ChooseLocationViewModel>(
    FragmentChooseLocationBinding::inflate, ChooseLocationViewModel::class.java
) {
    @Inject
    lateinit var locationManager: LocationManager

    var isUserClickLocationItem = false

    lateinit var fusedLocationClient: FusedLocationProviderClient

    var googleMap: GoogleMap? = null

    val permissions = listOf(
        Manifest.permission.ACCESS_FINE_LOCATION,
        Manifest.permission.ACCESS_COARSE_LOCATION,
    )

    lateinit var placesAdapter: PlacesAdapter

    val changeLocationScreenType: ChooseLocationScreenType by lazy {
        arguments?.parcelable(Constant.KEY_CHANGE_LOCATION_MODE) ?: ChooseLocationScreenType.CreateNew
    }

    val isCreateNewLocation by lazy {
        changeLocationScreenType is ChooseLocationScreenType.CreateNew
    }

    val isEditLocationGlobally by lazy {
        changeLocationScreenType is ChooseLocationScreenType.EditLocationGlobally
    }

    var previousLocationInfo: LocationInfo? = null

    var isApplySave = false

    val updatedLocationInfoWithLayoutItem: LocationInfoWithLayoutItem?
        get() = (changeLocationScreenType as? ChooseLocationScreenType.EditLocationGlobally)?.locationInfoWithLayoutItem

    override fun init(view: View) {
        previousLocationInfo = commonViewModel.commonUiState.value.defaultLocationInfo?.copy()
        setupBackButton()
        setUpScreenTitle()
        setupMap()
        setupDoneButton()
        setUpSearchAction()
    }

    override fun subscribeObserver(view: View) {
        observeSelectedLocation()
    }
}

sealed class ChooseLocationScreenType {
    @Parcelize
    data object CreateNew : ChooseLocationScreenType(), Parcelable

    @Parcelize
    data class EditLocationGlobally(
        val locationInfoWithLayoutItem: LocationInfoWithLayoutItem? = null
    ) : ChooseLocationScreenType(), Parcelable
}
