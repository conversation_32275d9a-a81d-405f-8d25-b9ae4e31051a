package co.cameralocation.framework.presentation.editlayout.adapter

import android.text.Editable
import android.text.TextWatcher
import android.view.View
import co.cameralocation.R
import co.cameralocation.databinding.ItemLayoutSettingBinding
import co.cameralocation.framework.presentation.common.BaseListAdapter
import co.cameralocation.framework.presentation.common.createDiffCallback
import co.cameralocation.framework.presentation.editlayout.EditLayoutDialogFragment
import co.cameralocation.framework.presentation.editlayout.model.LayoutSettingItem
import co.cameralocation.util.hideKeyboard
import co.cameralocation.util.setPreventDoubleClick

class LayoutSettingAdapter : BaseListAdapter<LayoutSettingItem, ItemLayoutSettingBinding>(
    createDiffCallback(
        areItemsTheSame = { oldItem, newItem -> oldItem.id == newItem.id },
        areContentsTheSame = { oldItem, newItem -> oldItem == newItem }
    )
) {
    var onSwitchChanged: ((LayoutSettingItem, Boolean) -> Unit)? = null
    var onSaveTitle: ((String) -> Unit)? = null
    var textWatcher: TextWatcher? = null

    fun updateTitleText(title: String) {
        val titleItem = currentList.find { it.id == EditLayoutDialogFragment.ITEM_ID_TITLE }
        titleItem?.let {
            it.editTextValue = title
            notifyItemChanged(currentList.indexOf(it))
        }
    }

    fun updateSwitchState(id: String, isChecked: Boolean) {
        val item = currentList.find { it.id == id }
        item?.let {
            it.isEnabled = isChecked
            notifyItemChanged(currentList.indexOf(it))
        }
    }

    override fun getLayoutRes(viewType: Int): Int = R.layout.item_layout_setting

    override fun bindView(
        binding: ItemLayoutSettingBinding,
        item: LayoutSettingItem,
        position: Int
    ) {

        binding.apply {
            titleTextView.text = item.title

            // Setup switch
            switchSetting.setOn(item.isEnabled)
            switchSetting.isEnabled = !item.isFixed

            // Setup edit text
            if (item.hasEditText) {
                editTextLayout.visibility = View.VISIBLE

                // Remove previous text watcher to avoid callback loops
                textWatcher?.let { editText.removeTextChangedListener(it) }

                // Set text
                editText.setText(item.editTextValue)
                btnSave.isEnabled = false
                btnSave.alpha = 0.3f
                btnSave.text = "Saved"

                // Add new text watcher
                textWatcher = object : TextWatcher {
                    override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
                    override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
                    override fun afterTextChanged(s: Editable?) {
                        if (s.toString().isNotBlank() && s.toString() != item.editTextValue) {
                            btnSave.isEnabled = true
                            btnSave.alpha = 1f
                            btnSave.text = "Save"
                        } else {
                            btnSave.isEnabled = false
                            btnSave.alpha = 0.3f
                            btnSave.text = "Saved"
                        }

                        item.editTextValue = s.toString()
                    }
                }

                editText.addTextChangedListener(textWatcher)

                // Setup save button
                btnSave.setPreventDoubleClick {
                    onSaveTitle?.invoke(editText.text.toString())
                    editText.clearFocus()
                    editText.hideKeyboard()
                }
            } else {
                editTextLayout.visibility = View.GONE
            }

            // Setup switch listener
            switchSetting.setOnToggledListener {_, isChecked ->
                if (!item.isFixed) {
                    onSwitchChanged?.invoke(item, isChecked)
                }
            }
        }
    }

    override fun bindView(
        binding: ItemLayoutSettingBinding,
        item: LayoutSettingItem,
        position: Int,
        payloads: MutableList<Any>
    ) {
        bindView(binding, item, position)
    }

}