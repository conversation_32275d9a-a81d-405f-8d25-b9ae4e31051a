package co.cameralocation.framework.presentation.editlayout

import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import co.cameralocation.framework.presentation.editlocation.adapter.LayoutItem
import co.cameralocation.framework.presentation.common.BaseViewModel
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfoWithLayoutItem
import co.cameralocation.util.PrefUtil
import javax.inject.Inject

@HiltViewModel
class EditLayoutViewModel @Inject constructor(
    private val prefUtil: PrefUtil
) : BaseViewModel() {

    private val _uiState = MutableStateFlow(EditLayoutUiState())
    val uiState = _uiState.asStateFlow()

    fun loadDefaultUnknownLocationTitleGlobally(initialValue: String) {
        _uiState.update { state ->
            state.copy(
                defaultUnknownLocationTitle = EditLayoutTitleUiState(
                    title = initialValue,
                    isFromLoadInitialData = true
                )
            )
        }
    }

    fun saveDefaultUnknownLocationTitleGlobally(title: String) {
        _uiState.update { state ->
            state.copy(
                defaultUnknownLocationTitle = EditLayoutTitleUiState(
                    title = title,
                    isFromLoadInitialData = false
                )
            )
        }
    }

    fun toggleShowCoordinates(show: Boolean) {
        _uiState.update { state ->
            state.copy(showCoordinates = show)
        }
    }

    fun toggleShowDateTime(show: Boolean) {
        _uiState.update { state ->
            state.copy(showDateTime = show)
        }
    }

    fun toggleShowWeather(show: Boolean) {
        _uiState.update { state ->
            state.copy(showWeather = show)
        }
    }
}

data class EditLayoutUiState(
    val locationInfoWithLayoutItem: LocationInfoWithLayoutItem? = null,
    val defaultUnknownLocationTitle: EditLayoutTitleUiState = EditLayoutTitleUiState(),
    val showCoordinates: Boolean? = null,
    val showDateTime: Boolean? = null,
    val showWeather: Boolean? = null
)

data class EditLayoutTitleUiState(
    val title: String = "",
    val isFromLoadInitialData: Boolean = false
)