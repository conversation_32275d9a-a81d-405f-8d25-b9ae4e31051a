package co.cameralocation.framework.presentation.chooselocation

import android.location.Address
import android.location.Geocoder
import android.os.Build
import androidx.core.os.bundleOf
import androidx.core.view.isVisible
import androidx.fragment.app.setFragmentResult
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import co.cameralocation.R
import co.cameralocation.framework.network.weather.WeatherResponse
import co.cameralocation.framework.presentation.chooselocation.adapter.PlacesAdapter
import co.cameralocation.framework.presentation.chooselocation.adapter.PlacesItem
import co.cameralocation.framework.presentation.common.launchMain
import co.cameralocation.framework.presentation.model.locationinfo.LocationInfo
import co.cameralocation.framework.presentation.model.locationinfo.toLatLng
import co.cameralocation.framework.repository.Result
import co.cameralocation.util.Constant
import co.cameralocation.util.collectFlowOnView
import co.cameralocation.util.displayToast
import co.cameralocation.util.hideKeyboard
import co.cameralocation.util.isAllPermissionGranted
import co.cameralocation.util.marquee
import co.cameralocation.util.requestPermission
import co.cameralocation.util.setPreventDoubleClick
import co.cameralocation.util.textChangesFlow
import com.google.android.gms.location.LocationServices
import com.google.android.gms.maps.CameraUpdateFactory
import com.google.android.gms.maps.SupportMapFragment
import com.google.android.gms.maps.model.LatLng
import com.google.maps.android.ktx.addMarker
import com.google.maps.android.ktx.awaitMap
import kotlinx.coroutines.CompletableDeferred
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.filterNot
import kotlinx.coroutines.flow.filterNotNull
import kotlinx.coroutines.flow.launchIn
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.onEach
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import java.util.Locale

fun ChooseLocationFragment.setUpScreenTitle() {
    binding.titleText.text = when {
        isCreateNewLocation -> getString(R.string.add_location)
        else -> getString(R.string.edit_location)
    }
}

fun ChooseLocationFragment.setupMap() {
    fusedLocationClient = LocationServices.getFusedLocationProviderClient(requireActivity())
    launchMain {
        val mapFragment =
            childFragmentManager.findFragmentById(R.id.mapChooseLocation) as? SupportMapFragment

        googleMap = mapFragment?.awaitMap()

        googleMap?.let { map ->
            try {
                if (!isAllPermissionGranted(permissions)) {
                    requestPermission(permissions) {}
                    return@launchMain
                }

                map.isMyLocationEnabled = true

                map.setOnMapClickListener { latLng ->
                    updateSelectedLocation(latLng)
                }

                // Use Maps KTX to get current location
                val location = fusedLocationClient.lastLocation.await()
                val currentLatLng = LatLng(location.latitude, location.longitude)

                if (isEditLocationGlobally) {
                    commonViewModel.setDefaultLocationInfo(updatedLocationInfoWithLayoutItem?.locationInfo)
                }

                updateSelectedLocation(commonViewModel.commonUiState.value.defaultLocationInfo?.toLatLng() ?: currentLatLng)
            } catch (e: SecurityException) {
                displayToast(getString(R.string.error_location_permission, e.message))
            }
        }
    }
}

fun ChooseLocationFragment.updateSelectedLocation(latLng: LatLng, updateId: Long? = null) {
    Timber.d("updateSelectedLocation: latLng: $latLng, updateId: $updateId")
    viewLifecycleOwner.lifecycleScope.launch {
        val locationName = locationManager.getAddressFromLocation(
            requireContext(), latLng.latitude, latLng.longitude
        )

        val result = CompletableDeferred<Result<WeatherResponse>>()
        viewModel.getCurrentWeatherByCoordinates(
            latitude = latLng.latitude,
            longitude = latLng.longitude,
            language = Locale.getDefault().language,
            onSuccess = { weatherResponse ->
                Timber.d("getCurrentWeatherByCoordinates: weatherResponse: $weatherResponse")
                result.complete(Result.Success(weatherResponse))
            },
            onError = {
                Timber.d("getCurrentWeatherByCoordinates: error")
                result.complete(Result.Error(Exception("Error getting weather")))
            }
        )

        val weatherResponse = result.await()
        val weatherData = when (weatherResponse) {
            is Result.Success -> weatherResponse.data
            is Result.Error -> null
        }

        val locationInfo = LocationInfo(
            id = updateId,
            name = locationName ?: requireContext().getString(R.string.unknown),
            latitude = latLng.latitude,
            longitude = latLng.longitude,
            temperature = weatherData?.main?.temp,
            weatherCondition = weatherData?.weather?.firstOrNull()?.main
        )

        commonViewModel.setDefaultLocationInfo(locationInfo)

        // Update map marker
        googleMap?.let { map ->
            map.clear()
            map.addMarker {
                position(latLng)
            }
            map.moveCamera(CameraUpdateFactory.newLatLngZoom(latLng, 15f))
        }
    }
}

fun ChooseLocationFragment.observeSelectedLocation() {
    commonViewModel.commonUiState
        .map { it.defaultLocationInfo }
        .filterNotNull()
        .collectFlowOnView(viewLifecycleOwner, Lifecycle.State.RESUMED) { locationInfo ->
            binding.tvCurrentLocationName.text = locationInfo.name
            binding.tvCurrentLocationName.marquee()
            binding.tvCurrentLocationCoordinates.text = "${locationInfo.latitude}, ${locationInfo.longitude}"
        }
}

fun ChooseLocationFragment.setupDoneButton() {
    binding.btnDone.setPreventDoubleClick {
        isApplySave = true
        val locationInfo = commonViewModel.commonUiState.value.defaultLocationInfo

        if (locationInfo == null) {
            displayToast(getString(R.string.please_select_location))
            return@setPreventDoubleClick
        }

        if (isCreateNewLocation) {
            val locationInfoWithLayoutItem = commonViewModel.createDefaultLayoutItemDataWhenCreateNewLocation(locationInfo)

            viewModel.saveNewLocationToDatabase(locationInfoWithLayoutItem) {
                launchMain {
                    displayToast(getString(R.string.save_new_location))
                    backEvent()
                }
            }
        } else {
            val locationInfoWithLayoutItem = updatedLocationInfoWithLayoutItem?.copy(
                locationInfo = locationInfo,
            )

            viewModel.updateLocationToDatabase(
                locationInfoWithLayoutItem,
                onSuccess = {
                    launchMain {
                        displayToast(getString(R.string.update_location_success))
                        backEvent()
                    }
                },
                onError = {
                    launchMain {
                        isApplySave = false
                        displayToast(getString(R.string.update_location_failed))
                    }
                }
            )
        }
    }
}


fun ChooseLocationFragment.backEvent() {
    setFragmentResult(
        Constant.KEY_TRIGGER_RELOAD_LOCATION_DATA,
        bundleOf(Constant.KEY_TRIGGER_RELOAD_LOCATION_DATA to true)
    )

    if (!isApplySave) {
        commonViewModel.setDefaultLocationInfo(previousLocationInfo)
    }

    activity?.onBackPressedDispatcher?.onBackPressed()
}

fun ChooseLocationFragment.setupBackButton() {
    binding.btnBack.setPreventDoubleClick {
        backEvent()
    }
}

fun ChooseLocationFragment.createPlacesList(addresses: List<Address>): List<PlacesItem> {
    return addresses.mapIndexed { index, address ->
        val placeName = listOfNotNull(
            address.featureName?.plus(address.thoroughfare?.let { ", $it" } ?: ""),
            address.thoroughfare,
            address.subLocality,
            address.locality,
            address.adminArea,
            address.countryName
        ).firstOrNull() ?: "Unknown place"

        val placeAddress = address.getAddressLine(0) ?: buildString {
            append(address.featureName ?: "")
            if (!address.locality.isNullOrEmpty()) append(", ${address.locality}")
            if (!address.countryName.isNullOrEmpty()) append(", ${address.countryName}")
        }

        PlacesItem(
            id = index,
            placeName = placeName,
            placeAddressName = placeAddress,
            address = address
        )
    }
}

@OptIn(FlowPreview::class)
fun ChooseLocationFragment.setUpSearchAction() {
    // Setup recycler view
    placesAdapter = PlacesAdapter()
    placesAdapter.setOnItemClickListener { address ->
        isUserClickLocationItem = true
        getPlaceDetails(address)
        binding.searchInput.clearFocus()
        binding.searchInput.hideKeyboard()
    }

    binding.placesRecyclerView.adapter = placesAdapter

    // Setup search functionality
    binding.searchButton.setOnClickListener {
        performSearch()
    }

    binding.searchInput.textChangesFlow()
        .debounce(300)
        .filterNot { isUserClickLocationItem == true }
        .onEach {
            performSearch()
        }
        .launchIn(viewLifecycleOwner.lifecycleScope)
}

fun ChooseLocationFragment.performSearch() {
    val query = binding.searchInput.text.toString().trim()
    if (query.isEmpty()) {
        Timber.d("performSearch: query is empty")
        return
    }

    viewLifecycleOwner.lifecycleScope.launch {
        try {
            val geocoder = Geocoder(requireContext())

            // Use getFromLocationName to get up to 5 nearest locations
            val addresses = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                // For Android 13+
                val addressList = mutableListOf<Address>()
                geocoder.getFromLocationName(query, 5) { addresses ->
                    addressList.addAll(addresses)
                }
                // Wait a moment for the callback to complete
                delay(500)
                addressList
            } else {
                // For Android 12 and below
                geocoder.getFromLocationName(query, 5) ?: emptyList()
            }

            Timber.d("performSearch: addresses: $addresses")
            val places = createPlacesList(addresses)
            binding.placesRecyclerView.isVisible = places.isNotEmpty()
            placesAdapter.submitList(places)
        } catch (e: Exception) {
            Timber.e(e)
            displayToast(getString(R.string.place_search_failed, e.message ?: getString(R.string.unknown)))
        }
    }
}

fun ChooseLocationFragment.getPlaceDetails(address: Address) {
    binding.placesRecyclerView.isVisible = false

    try {
        val latLng = LatLng(address.latitude, address.longitude)
        Timber.d("getPlaceDetails: latLng: $latLng")
        updateSelectedLocation(latLng)

        // Set the full address in the search input
        val addressText = address.getAddressLine(0) ?:
            "${address.featureName ?: ""}, ${address.locality ?: ""}, ${address.countryName ?: ""}"
        binding.searchInput.setText(addressText)
    } catch (e: Exception) {
        Timber.e(e)
        displayToast(getString(R.string.place_details_failed, e.message ?: getString(R.string.unknown)))
    }
}
